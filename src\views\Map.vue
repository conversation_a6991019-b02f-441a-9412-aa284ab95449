<template>
    <div style="margin-top: 10px;margin-left: 20px;">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="中心点id：">
                    <n-input-number v-model:value="centerId" clearable placeholder="输入中心点id" />
                </n-form-item>
                <n-checkbox-group v-model:value="directions">
                    <n-space item-style="display: flex;">
                    <n-checkbox value="topId" label="上" />
                    <n-checkbox value="bottomId" label="下" />
                    <n-checkbox value="leftId" label="左" />
                    <n-checkbox value="rightId" label="右" />
                    </n-space>
                </n-checkbox-group>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
                <n-form-item><n-tag type="info">调整中心点id以显示更多地图</n-tag></n-form-item>
                <n-form-item><n-tag type="info">超过遍历层数就会显示未展示</n-tag></n-form-item>
            </n-form>
            
        </div>
    <div id="content">
        
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="选择操作" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <template #header-extra>
                <n-button @click="directionClick('edit')">修改</n-button>
                <n-button @click="connectMap('bottom')">向上连接地图</n-button>
                <n-button @click="connectMap('left')">向右连接地图</n-button>
                <n-button @click="deleteMap">删除地图</n-button>
                <n-button @click="toGenerayList(mapDetail.id)">查看npc</n-button>
            </template>
            <div>
                <div class="flex-center"><n-button @click="directionClick('top')">上</n-button></div>
                <div class="flex-sb">
                    <n-button @click="directionClick('left')">左</n-button>
                    <n-button @click="directionClick('right')">右</n-button>
                </div>
                <div class="flex-center"><n-button @click="directionClick('bottom')">下</n-button></div>
            </div>
        </n-card>
    </n-modal>
    <n-modal v-model:show="showModalDetail">
        <n-card style="width: 600px" title="添加/编辑地图信息" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="地图名称">
                    <n-input placeholder="请输入地图名称" v-model:value="mapDetail.title">
                        <template #prefix>
                            <n-icon :component="FlashOutline" />
                        </template>
                    </n-input>
                </n-form-item>

                <div style="margin-top: 20px;">
                    <n-form-item label="地图描述">
                        <n-input v-model:value="mapDetail.desc" type="textarea" placeholder="请输入地图描述" />
                    </n-form-item>
                </div>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { h, inject, onMounted, reactive, ref } from 'vue';
import { FlashOutline } from '@vicons/ionicons5'
import { AxiosInstance } from 'axios';
import { NInput } from 'naive-ui';
import router from '../router';
const changeSelectedKey=inject('changeSelectedKey') as Function
const $api = inject('$api') as AxiosInstance
interface MapItem {
    id: number
    title: string
    topId?: number
    leftId?: number
    rightId?: number
    bottomId?: number
    desc?: string
}
const mapData: MapItem[] = reactive([

])
let connectId = ref('')
let showModal = ref(false)
let showModalDetail = ref(false)
let container: Element | null;
let centerId = ref(5050)//中心点id
let directions = ref(['leftId', 'rightId', 'topId', 'bottomId'])//中心点id
const reverseDirection = (direction: string) => {
    switch (direction) {
        case "topId":
            return "bottomId";
        case "bottomId":
            return "topId";
        case "leftId":
            return "rightId";
        case "rightId":
            return "leftId";
    }
}
const init = async () => {
    let objMap = []

    for (let i = 0; i < mapData.length; i++) {
        let item: MapItem = mapData[i]
        if (!i) {
            objMap.push(item.id)
            let newBox = document.createElement("div");
            newBox.className = "box123 box" + item.id;
            newBox.dataset.id = String(item.id);
            newBox.textContent = item.title + '(' + item.id + ')';
            newBox.style.top = `${window.innerHeight / 2 - 15}px`;
            newBox.style.left = `${window.innerWidth / 2 - 50}px`;
            container && container.appendChild(newBox);

        }
        for (let j = 0; j < 4; j++) {
            let direction = (directions.value)[j]
            let newBox = document.getElementsByClassName('box' + item.id)[0] as HTMLDivElement;
            let itemId = item[direction as keyof MapItem] as number
            if (itemId) {
                if (!objMap.includes(itemId)) {
                    let title = mapData.filter(item1 => item1.id == itemId)[0]?.title
                    objMap.push(itemId)
                    await addBox(newBox, { direction, title, id: itemId })
                } else {

                    //只连线
                    let lines = document.getElementsByClassName('line' + itemId + reverseDirection(direction));
                    let lines1 = document.getElementsByClassName('line' + item.id + direction);

                    if (!lines.length && !lines1.length) {
                        let line = document.createElement("div");
                        line.className = "line line" + itemId + reverseDirection(direction);
                        container && container.appendChild(line);
                        switch (direction) {
                            case "topId":
                                line.style.width = "2px";
                                line.style.height = "50px";
                                line.style.top = `${newBox.offsetTop - 50}px`;
                                line.style.left = `${newBox.offsetLeft + 49}px`;
                                break;
                            case "bottomId":
                                line.style.width = "2px";
                                line.style.height = "50px";
                                line.style.top = `${newBox.offsetTop + 30}px`;
                                line.style.left = `${newBox.offsetLeft + 49}px`;
                                break;
                            case "leftId":
                                line.style.width = "50px";
                                line.style.height = "2px";
                                line.style.top = `${newBox.offsetTop + 15}px`;
                                line.style.left = `${newBox.offsetLeft - 50}px`;
                                break;
                            case "rightId":
                                line.style.width = "50px";
                                line.style.height = "2px";
                                line.style.top = `${newBox.offsetTop + 15}px`;
                                line.style.left = `${newBox.offsetLeft + 100}px`;
                                break;
                        }
                    }

                }
            }
        }
    }
}
//搜索
const searchBtn = () => {
    getData(centerId.value)
}
//地图属性
const mapDetail = ref({
    title: '',
    desc: '',
    direction: '',
    id: 0
})
const addBox = (targetBox: HTMLDivElement, { direction, title, id }: { direction: string, title: string, id: number }) => {
    return new Promise((resolve) => {
        let newBox = document.createElement("div");
        newBox.className = "box123 box" + id;
        newBox.dataset.id = String(id);
        newBox.textContent = (title||'未展示') + '(' + id + ')';
        //添加点击事件
        let line = document.createElement("div");
        line.className = "line line" + id + reverseDirection(direction);

        container && container.appendChild(newBox);
        container && container.appendChild(line);
        switch (direction) {
            case "topId":
                newBox.style.top = `${targetBox.offsetTop - 80}px`;
                newBox.style.left = `${targetBox.offsetLeft}px`;
                line.style.width = "2px";
                line.style.height = "50px";
                line.style.top = `${targetBox.offsetTop - 50}px`;
                line.style.left = `${targetBox.offsetLeft + 49}px`;
                break;
            case "bottomId":
                newBox.style.top = `${targetBox.offsetTop + 80}px`;
                newBox.style.left = `${targetBox.offsetLeft}px`;
                line.style.width = "2px";
                line.style.height = "50px";
                line.style.top = `${targetBox.offsetTop + 30}px`;
                line.style.left = `${targetBox.offsetLeft + 49}px`;
                break;
            case "leftId":
                newBox.style.top = `${targetBox.offsetTop}px`;
                newBox.style.left = `${targetBox.offsetLeft - 150}px`;
                line.style.width = "50px";
                line.style.height = "2px";
                line.style.top = `${targetBox.offsetTop + 15}px`;
                line.style.left = `${targetBox.offsetLeft - 50}px`;
                break;
            case "rightId":
                newBox.style.top = `${targetBox.offsetTop}px`;
                newBox.style.left = `${targetBox.offsetLeft + 150}px`;
                line.style.width = "50px";
                line.style.height = "2px";
                line.style.top = `${targetBox.offsetTop + 15}px`;
                line.style.left = `${targetBox.offsetLeft + 100}px`;
                break;
        }
        resolve(true);
    })
}
const directionClick = (type: string) => {
    //没有方向就是修改
    if (type != 'edit') {
        mapDetail.value.direction = type
    } else {
        let map = mapData.filter(item => item.id == mapDetail.value.id)[0]
        mapDetail.value.title = map.title
        mapDetail.value.desc = map.desc as string
    }
    showModal.value = false
    showModalDetail.value = true

}
//跳转npc页面
const toGenerayList = (mapId: number) => {
    changeSelectedKey('npc')
    router.push({ path: '/npc', query: { mapId: mapId } })
}
//添加地图
const saveMsg = () => {
    // if(!mapDetail.value.title||!mapDetail.value.desc){
    //     window.$message.error('地图名和描述不能为空')
    //     return
    // }
    if (!mapDetail.value.direction) {
        $api.post('/admin/updateMap', {
            id: mapDetail.value.id,
            title: mapDetail.value.title,
            desc: mapDetail.value.desc,
        }).then((res) => {
            if (res.data.code == 200) {
                window.$message.success('修改成功')
                getData(centerId.value)
            } else {
                window.$message.error('修改失败')
            }

        })
    } else {
        $api.post('/admin/addmap', {
            title: mapDetail.value.title,
            desc: mapDetail.value.desc,
            directionType: mapDetail.value.direction,
            sourceId: mapDetail.value.id
        }).then(res => {
            if (res.data.code == 200) {
                window.$message.success('修改成功')
                getData(centerId.value)
            } else {
                window.$message.error('修改失败')
            }
        })
    }
}
const getData = (id=5050) => {
    showModalDetail.value = false
    showModal.value = false
    $api.post('/admin/getMaps', {id}).then((res) => {
        if (container) container.innerHTML = ''
        mapData.length = 0
        mapData.push(...res.data)
        init()
    })
}
//删除连接
const deleteLine = (id: number, directionName: string) => {
    window.$dialog.warning({
        title: '提示',
        content: '是否删除当前连接？',
        positiveText: '确定',
        onPositiveClick: () => {
            $api.post('/admin/deleteLine',
                {
                    id,
                    directionType: directionName
                })
                .then(res => {
                    if (res.data.code == 200) {
                        window.$message.success('修改成功')
                        getData()
                    } else {
                        window.$message.error('修改失败')
                    }
                })

        }
    })
}
//删除地图
const deleteMap = () => {
    // window.$message.success('禁止删除地图')
    // return

    window.$dialog.warning({
        title: '提示',
        content: '是否删除地图？',
        positiveText: '确定',
        onPositiveClick: () => {
            $api.post('/admin/deleteMap',
                {
                    id: mapDetail.value.id
                })
                .then(res => {
                    if (res.data.code == 200) {
                        window.$message.success('删除成功')
                        getData()
                    } else {
                        window.$message.error('删除失败')
                    }
                })

        }
    })

}
//连接地图
const connectMap = (type: string) => {
    window.$dialog.warning({
        title: '请输入要连接的地图id',
        content: () => h('div', {}, [h(NInput, {
            value: connectId.value,
            onUpdateValue: (value) => {
                connectId.value = value
            }
        })]),
        positiveText: '确定',
        onPositiveClick: () => {
            $api.post('/admin/lineMap',
                {
                    id: mapDetail.value.id,
                    directionType: type,
                    sourceId: Number(connectId.value)
                })
                .then(res => {
                    if (res.data.length) {
                        window.$message.success('修改成功')
                        getData()
                    } else {
                        window.$message.error('修改失败')
                    }
                })

        }
    })

}
onMounted(() => {
    container = document.getElementById("content");
    document.addEventListener('contextmenu', function (event) {
        // 阻止默认的右键菜单弹出
        event.preventDefault();
        let target = event.target as HTMLDivElement
        if (target.tagName != 'DIV') return
        if (target.className && target.className.includes('box123')) {
            centerId.value = Number(target.dataset.id)
            getData(centerId.value)
        }
    }); 
    document.addEventListener("click", (e) => {
        let target = e.target as HTMLDivElement
        if (target.tagName != 'DIV') return
        if (target.className && target.className.includes('box123')) {
            if(target.innerText.includes('未展示')){
                window.$message.error('无法修改未展示地图')
                return
            }
            showModalDetail.value = false
            mapDetail.value = { title: '', desc: '', direction: '', id: 0 }
            showModal.value = true
            mapDetail.value.id = Number(target.dataset.id as string)
        }
        if (target.className && target.className.includes('line')) {
            let className = target.className.split(' ')[1].slice(4)
            var match = className.match(/\d+/);
            if (match) {
                let restOfString = className.substring(match[0].length);
                deleteLine(Number(match[0]), restOfString)
            }
        }
    })
    getData()

});
</script>

<style>
#content {
    position: relative;
    height: 100vh;
    user-select: none;
}

#content .box123 {
    width: 100px;
    height: 30px;
    background-color: #007bff;
    position: absolute;
    color: white;
    font-weight: bold;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    line-height: 30px;
    white-space: nowrap;
}

#content .line {
    position: absolute;
    background-color: black;
    cursor: pointer;
}

.flex-sb {
    display: flex;
    justify-content: space-between;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>